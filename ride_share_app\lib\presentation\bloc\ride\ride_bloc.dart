import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:uuid/uuid.dart';
import '../../../data/services/location_service.dart';
import '../../../data/models/location_model.dart';
import '../../../core/constants/app_constants.dart';
import 'ride_event.dart';
import 'ride_state.dart';

class RideBloc extends Bloc<RideEvent, RideState> {
  final LocationService _locationService;
  final Uuid _uuid = const Uuid();

  RideBloc({required LocationService locationService})
      : _locationService = locationService,
        super(RideInitial()) {
    on<RideInitialized>(_onRideInitialized);
    on<PickupLocationSelected>(_onPickupLocationSelected);
    on<DropoffLocationSelected>(_onDropoffLocationSelected);
    on<CurrentLocationRequested>(_onCurrentLocationRequested);
    on<RouteCalculated>(_onRouteCalculated);
    on<RideRequested>(_onRideRequested);
    on<RideCancelled>(_onRideCancelled);
    on<RideAccepted>(_onRideAccepted);
    on<RideStarted>(_onRideStarted);
    on<RideCompleted>(_onRideCompleted);
    on<RideRated>(_onRideRated);
    on<RideHistoryRequested>(_onRideHistoryRequested);
  }

  Future<void> _onRideInitialized(
    RideInitialized event,
    Emitter<RideState> emit,
  ) async {
    emit(RideLoading());
    try {
      final currentLocation = await _locationService.getCurrentLocation();
      emit(RideLocationSelecting(currentLocation: currentLocation));
    } catch (e) {
      emit(RideError(message: 'Failed to get current location: $e'));
    }
  }

  Future<void> _onCurrentLocationRequested(
    CurrentLocationRequested event,
    Emitter<RideState> emit,
  ) async {
    try {
      final currentLocation = await _locationService.getCurrentLocation();
      if (state is RideLocationSelecting) {
        final currentState = state as RideLocationSelecting;
        emit(RideLocationSelecting(
          pickupLocation: currentState.pickupLocation,
          dropoffLocation: currentState.dropoffLocation,
          currentLocation: currentLocation,
        ));
      }
    } catch (e) {
      emit(RideError(message: 'Failed to get current location: $e'));
    }
  }

  Future<void> _onPickupLocationSelected(
    PickupLocationSelected event,
    Emitter<RideState> emit,
  ) async {
    if (state is RideLocationSelecting) {
      final currentState = state as RideLocationSelecting;
      emit(RideLocationSelecting(
        pickupLocation: event.location,
        dropoffLocation: currentState.dropoffLocation,
        currentLocation: currentState.currentLocation,
      ));

      // If both locations are selected, calculate route
      if (currentState.dropoffLocation != null) {
        add(RouteCalculated(
          pickup: event.location,
          dropoff: currentState.dropoffLocation!,
        ));
      }
    }
  }

  Future<void> _onDropoffLocationSelected(
    DropoffLocationSelected event,
    Emitter<RideState> emit,
  ) async {
    if (state is RideLocationSelecting) {
      final currentState = state as RideLocationSelecting;
      emit(RideLocationSelecting(
        pickupLocation: currentState.pickupLocation,
        dropoffLocation: event.location,
        currentLocation: currentState.currentLocation,
      ));

      // If both locations are selected, calculate route
      if (currentState.pickupLocation != null) {
        add(RouteCalculated(
          pickup: currentState.pickupLocation!,
          dropoff: event.location,
        ));
      }
    }
  }

  Future<void> _onRouteCalculated(
    RouteCalculated event,
    Emitter<RideState> emit,
  ) async {
    emit(RideLoading());
    try {
      // Calculate distance and duration
      final distance = _locationService.calculateDistance(event.pickup, event.dropoff);
      final estimatedDuration = _calculateDuration(distance);
      final baseFare = _calculateBaseFare(distance);

      // Generate ride options
      final rideOptions = _generateRideOptions(baseFare);

      emit(RideRouteCalculated(
        pickupLocation: event.pickup,
        dropoffLocation: event.dropoff,
        estimatedDistance: distance / 1000, // Convert to km
        estimatedDuration: estimatedDuration,
        estimatedFare: baseFare,
        rideOptions: rideOptions,
      ));
    } catch (e) {
      emit(RideError(message: 'Failed to calculate route: $e'));
    }
  }

  Future<void> _onRideRequested(
    RideRequested event,
    Emitter<RideState> emit,
  ) async {
    if (state is RideRouteCalculated) {
      final currentState = state as RideRouteCalculated;
      
      emit(RideRequesting(
        pickupLocation: currentState.pickupLocation,
        dropoffLocation: currentState.dropoffLocation,
        rideType: event.rideType,
      ));

      try {
        // Create ride model
        final ride = RideModel(
          id: _uuid.v4(),
          riderId: 'current_user_id', // Get from auth
          pickupLocation: currentState.pickupLocation,
          dropoffLocation: currentState.dropoffLocation,
          status: AppConstants.rideStatusRequested,
          fare: currentState.estimatedFare,
          paymentMethod: event.paymentMethod,
          requestedAt: DateTime.now(),
        );

        // Simulate ride request (in production, send to backend)
        await Future.delayed(const Duration(seconds: 2));

        emit(RideRequestedState(ride: ride));

        // Simulate driver acceptance after some time
        await Future.delayed(const Duration(seconds: 5));
        add(RideAccepted(rideId: ride.id, driverId: 'driver_123'));
      } catch (e) {
        emit(RideError(message: 'Failed to request ride: $e'));
      }
    }
  }

  Future<void> _onRideAccepted(
    RideAccepted event,
    Emitter<RideState> emit,
  ) async {
    if (state is RideRequestedState) {
      final currentState = state as RideRequestedState;
      
      // Create mock driver info
      const driver = DriverInfo(
        id: 'driver_123',
        name: 'John Driver',
        phoneNumber: '+**********',
        rating: 4.8,
        vehicleModel: 'Toyota Camry',
        vehicleColor: 'Blue',
        licensePlate: 'ABC-123',
      );

      final updatedRide = currentState.ride.copyWith(
        status: AppConstants.rideStatusAccepted,
        driverId: event.driverId,
        acceptedAt: DateTime.now(),
      );

      emit(RideAcceptedState(ride: updatedRide, driver: driver));
    }
  }

  Future<void> _onRideStarted(
    RideStarted event,
    Emitter<RideState> emit,
  ) async {
    if (state is RideAcceptedState) {
      final currentState = state as RideAcceptedState;
      
      final updatedRide = currentState.ride.copyWith(
        status: AppConstants.rideStatusInProgress,
        startedAt: DateTime.now(),
      );

      emit(RideInProgress(
        ride: updatedRide,
        driver: currentState.driver,
      ));
    }
  }

  Future<void> _onRideCompleted(
    RideCompleted event,
    Emitter<RideState> emit,
  ) async {
    if (state is RideInProgress) {
      final currentState = state as RideInProgress;
      
      final updatedRide = currentState.ride.copyWith(
        status: AppConstants.rideStatusCompleted,
        completedAt: DateTime.now(),
        fare: event.fare,
      );

      emit(RideCompletedState(ride: updatedRide, finalFare: event.fare));
    }
  }

  Future<void> _onRideRated(
    RideRated event,
    Emitter<RideState> emit,
  ) async {
    if (state is RideCompletedState) {
      final currentState = state as RideCompletedState;
      
      final updatedRide = currentState.ride.copyWith(
        rating: event.rating,
        review: event.review,
      );

      emit(RideRatedState(ride: updatedRide, rating: event.rating));
    }
  }

  Future<void> _onRideCancelled(
    RideCancelled event,
    Emitter<RideState> emit,
  ) async {
    emit(const RideLocationSelecting());
  }

  Future<void> _onRideHistoryRequested(
    RideHistoryRequested event,
    Emitter<RideState> emit,
  ) async {
    emit(RideLoading());
    try {
      // Mock ride history (in production, fetch from backend)
      final rides = _generateMockRideHistory();
      emit(RideHistoryLoaded(rides: rides));
    } catch (e) {
      emit(RideError(message: 'Failed to load ride history: $e'));
    }
  }

  // Helper methods
  double _calculateDuration(double distanceInMeters) {
    // Assume average speed of 30 km/h in city
    const averageSpeedKmh = 30.0;
    final distanceKm = distanceInMeters / 1000;
    return (distanceKm / averageSpeedKmh) * 60; // Return in minutes
  }

  double _calculateBaseFare(double distanceInMeters) {
    const baseFare = 5.0; // Base fare
    const perKmRate = 2.0; // Rate per km
    final distanceKm = distanceInMeters / 1000;
    return baseFare + (distanceKm * perKmRate);
  }

  List<RideOption> _generateRideOptions(double baseFare) {
    return [
      RideOption(
        type: 'economy',
        name: 'Economy',
        description: 'Affordable rides',
        fare: baseFare,
        estimatedArrival: 5,
        icon: Icons.directions_car,
      ),
      RideOption(
        type: 'comfort',
        name: 'Comfort',
        description: 'More space and comfort',
        fare: baseFare * 1.5,
        estimatedArrival: 3,
        icon: Icons.car_rental,
      ),
      RideOption(
        type: 'premium',
        name: 'Premium',
        description: 'Luxury vehicles',
        fare: baseFare * 2.0,
        estimatedArrival: 7,
        icon: Icons.local_taxi,
      ),
    ];
  }

  List<RideModel> _generateMockRideHistory() {
    // Generate mock ride history
    return List.generate(10, (index) {
      return RideModel(
        id: 'ride_$index',
        riderId: 'current_user_id',
        pickupLocation: const LocationModel(
          latitude: 37.7749,
          longitude: -122.4194,
          address: 'San Francisco, CA',
          name: 'Home',
        ),
        dropoffLocation: const LocationModel(
          latitude: 37.7849,
          longitude: -122.4094,
          address: 'Downtown SF, CA',
          name: 'Office',
        ),
        status: AppConstants.rideStatusCompleted,
        fare: 15.50 + (index * 2),
        paymentMethod: AppConstants.paymentCard,
        requestedAt: DateTime.now().subtract(Duration(days: index)),
        completedAt: DateTime.now().subtract(Duration(days: index, hours: -1)),
        rating: 4.0 + (index % 2),
      );
    });
  }
}
