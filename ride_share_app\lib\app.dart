import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'core/theme/app_theme.dart';
import 'core/constants/app_constants.dart';
import 'presentation/bloc/auth/auth_bloc.dart';
import 'presentation/bloc/auth/auth_state.dart';
import 'presentation/bloc/auth/auth_event.dart';
import 'presentation/bloc/ride/ride_bloc.dart';
import 'presentation/bloc/ride/ride_event.dart';
import 'presentation/screens/auth/login_screen.dart';
import 'presentation/screens/auth/register_screen.dart';
import 'presentation/screens/rider/rider_home_screen.dart';
import 'presentation/screens/driver/driver_home_screen.dart';
import 'presentation/screens/shared/splash_screen.dart';
import 'presentation/screens/demo/working_demo_screen.dart';

class App extends StatefulWidget {
  const App({super.key});

  @override
  State<App> createState() => _AppState();
}

class _AppState extends State<App> {
  @override
  void initState() {
    super.initState();
    // Initialize blocs after widget tree is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AuthBloc>().add(AuthStarted());
      context.read<RideBloc>().add(RideInitialized());
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      debugShowCheckedModeBanner: false,
      // Working demo version
      home: const WorkingDemoScreen(),
      routes: {
        '/login': (context) => const LoginScreen(),
        '/register': (context) => const RegisterScreen(),
        '/rider-home': (context) => const RiderHomeScreen(),
        '/driver-home': (context) => const DriverHomeScreen(),
      },
    );
  }
}
