{"aadc_info": {"age_group": 0}, "accessibility": {"captions": {"headless_caption_enabled": false}}, "account_info": [{"access_point": 17, "account_id": "fake-0003BFFD2B2C9AEC", "accountcapabilities": {"accountcapabilities/g42tslldmfya": -1, "accountcapabilities/g44tilldmfya": -1, "accountcapabilities/ge2dinbnmnqxa": -1, "accountcapabilities/ge2tkmznmnqxa": -1, "accountcapabilities/ge2tknznmnqxa": -1, "accountcapabilities/ge2tkobnmnqxa": -1, "accountcapabilities/ge3dgmjnmnqxa": -1, "accountcapabilities/ge3dgobnmnqxa": -1, "accountcapabilities/geydgnznmnqxa": -1, "accountcapabilities/geytcnbnmnqxa": -1, "accountcapabilities/gezdcnbnmnqxa": -1, "accountcapabilities/gezdsmbnmnqxa": -1, "accountcapabilities/geztenjnmnqxa": -1, "accountcapabilities/gi2tklldmfya": -1, "accountcapabilities/gu2dqlldmfya": -1, "accountcapabilities/gu4dmlldmfya": -1, "accountcapabilities/guydolldmfya": -1, "accountcapabilities/guzdslldmfya": -1, "accountcapabilities/haytqlldmfya": -1, "accountcapabilities/he4tolldmfya": -1}, "edge_account_age_group": 0, "edge_account_cid": "", "edge_account_environment": 0, "edge_account_environment_string": "login.microsoftonline.com", "edge_account_first_name": "<PERSON><PERSON>", "edge_account_is_test_on_premises_profile": false, "edge_account_last_name": "Na<PERSON>", "edge_account_location": "", "edge_account_oid": "", "edge_account_phone_number": "", "edge_account_puid": "", "edge_account_sovereignty": 0, "edge_account_tenant_id": "9188040d-6c67-4c5b-b112-36a304b66dad", "edge_account_type": 5, "edge_tenant_supports_msa_linking": false, "edge_wam_aad_for_app_account_type": 0, "email": "<EMAIL>", "full_name": "", "gaia": "fake-0003BFFD2B2C9AEC", "given_name": "", "hd": "", "is_supervised_child": -1, "is_under_advanced_protection": false, "last_downloaded_image_url_with_size": "", "locale": "", "picture_url": ""}], "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "arbitration_last_notification_shown": "*****************", "arbitration_using_experiment_config": false, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"available_dark_theme_options": "All", "chat_v2": {"ip_eligibility_status": {"last_checked_time": "*****************"}}, "edge_sidebar_visibility": {"_game_assist_": {"order": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": **********}}, "add_app_to_bottom": true, "order": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": **********}}, "edge_sidebar_visibility_debug": {"order_list": ["Search"], "order_raw_data": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"name": "Search", "pos": "**********"}}}, "enable_text_prediction_v2": true, "has_seen_welcome_page": false, "hub_app_non_synced_preferences": {"apps": {"06be1ebe-f23a-4bea-ae45-3120ad86cfea": {"last_path": ""}, "0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": {"last_path": ""}, "168a2510-04d5-473e-b6a0-828815a7ca5f": {"last_path": ""}, "1ec8a5a9-971c-4c82-a104-5e1a259456b8": {"last_path": ""}, "2354565a-f412-4654-b89c-f92eaa9dbd20": {"last_path": ""}, "25fe2d1d-e934-482a-a62f-ea1705db905d": {"last_path": ""}, "2caf0cf4-ea42-4083-b928-29b39da1182b": {"last_path": ""}, "35a43603-bb38-4b53-ba20-932cb9117794": {"last_path": ""}, "380c71d3-10bf-4a5d-9a06-c932e4b7d1d8": {"last_path": ""}, "439642fc-998d-4a64-8bb6-940ecaf6b60b": {"last_path": ""}, "523b5ef3-0b10-4154-8b62-10b2ebd00921": {"last_path": ""}, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": {"last_path": ""}, "698b01b4-557a-4a3b-9af7-a7e8138e8372": {"last_path": ""}, "76b926d6-3738-46bf-82d7-2ab896ddf70b": {"last_path": ""}, "7b52ae05-ae84-4165-b083-98ba2031bc22": {"last_path": ""}, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": {"last_path": ""}, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"last_path": ""}, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": {"last_path": ""}, "96defd79-4015-4a32-bd09-794ff72183ef": {"last_path": ""}, "9ce3c9c2-462f-4cc9-bbd7-57d656445be0": {"last_path": ""}, "a1a78183-6db3-4789-9e7c-84d157846d55": {"last_path": ""}, "bacc3c12-ebff-44b4-a0f8-ce8b69c9e047": {"last_path": ""}, "c814ae4d-fa0a-4280-a444-cb8bd264828b": {"last_path": ""}, "cd4688a9-e888-48ea-ad81-76193d56b1be": {"last_path": ""}, "d3ff4c56-a2b8-4673-ad13-35e7706cc9d1": {"last_path": ""}, "da15ec1d-543d-41c9-94b8-eb2bd060f2c7": {"last_path": ""}, "dadd1f1c-380c-4871-9e09-7971b6b15069": {"last_path": ""}, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": {"last_path": ""}}}, "hub_app_preferences": {}, "hub_cleanup_candidate_list_for_debug": [{"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "cleanup_done_v2"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}], "hub_cleanup_context_v2": {"cleanup_debug_info_v2_adjusted_engaged_app_count": 0, "cleanup_debug_info_v2_app_count_threshold": 1, "cleanup_debug_info_v2_current_sidebar_visibility": 0, "cleanup_debug_info_v2_discover_icon_enabled": true, "cleanup_debug_info_v2_dwell_time_in_secs": 10, "cleanup_debug_info_v2_engaged_app_count": 0, "cleanup_debug_info_v2_expected_sidebar_visibility": 0, "cleanup_debug_info_v2_is_tower_off_by_user": false, "cleanup_debug_info_v2_skip_user_generated_apps_for_threshold": true, "cleanup_debug_info_v2_user_generated_app_count": 0, "hub_app_cleanup_v2_done": true}, "recent_theme_color_list": [4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0], "show_hub_app_in_sidebar_buttons": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0}, "show_hub_app_in_sidebar_buttons_legacy": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0}, "show_hub_app_in_sidebar_buttons_legacy_update_time": "13393531476290590", "underside_chat_bing_signed_in_status": false, "user_level_features_context": {}, "window_placement": {"bottom": 1021, "left": 210, "maximized": false, "right": 1155, "top": 9, "work_area_bottom": 1032, "work_area_left": 0, "work_area_right": 1920, "work_area_top": 0}}, "browser_content_container_height": 923, "browser_content_container_width": 929, "browser_content_container_x": 0, "browser_content_container_y": 81, "commerce_daily_metrics_last_update_time": "13393518413812066", "countryid_at_install": 21843, "custom_links": {"list": []}, "devtools": {"preferences": {"EdgeDevToolsLayoutInfo": {"current_dock_state": 0, "horizontal_size": 300, "showEmulationMode": false, "vertical_size": 555}}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "dual_engine": {"consumer_site_list_with_ie_entries": false, "consumer_sitelist_location": "", "consumer_sitelist_version": "", "external_consumer_shared_cookie_data": {}, "shared_cookie_data": {}, "sitelist_has_consumer_data": false, "sitelist_has_enterprise_data": false, "sitelist_location": "", "sitelist_source": 0, "sitelist_version": ""}, "edge": {"account_type": 5, "bookmarks": {"last_dup_info_record_time": "*****************"}, "msa_sso_info": {"allow_for_non_msa_profile": true}, "profile_matches_os_primary_account": false, "profile_sso_info": {"aad_sso_algo_state": 1, "is_first_profile": true, "is_msa_first_profile": true, "msa_sso_algo_state": 1}, "profile_sso_option": 1, "services": {"last_gaia_id": "fake-0003BFFD2B2C9AEC", "signin_scoped_device_id": "a213ec06-cf33-4006-811a-c8329485b189"}}, "edge_rewards": {"cache_data": "CAA=", "coachmark_promotions": {}, "hva_promotions": [], "refresh_status_muted_until": "*****************"}, "edge_ux_config": {"assignmentcontext": "XEe6qNAcvQhq6Zs1tuICNE8LWVsOlyKsSyxSOgYx+bU=", "dataversion": "*********", "experimentvariables": {"2f717976": {"edgeServerUX.sync.historyDataTypeEnabled": true}, "shop-60c": {"edgeServerUX.shopping.aablockth": 60, "edgeServerUX.shopping.block99": false}, "shopppdismisstreatment": {"edgeServerUX.shopping.msEdgeShoppingCashbackDismissTimeout2s": true}, "shoprevenuattributiont": {"edgeServerUX.shopping.disableCashbackOnCouponCopy": true}}, "flights": {"2f717976": "********", "shop-60c": "********", "shopppdismisstreatment": "31004791", "shoprevenuattributiont": "31235887"}, "latestcorrelationid": "Ref A: B26E907CEE4743ABBC6A6EBD7F944A75 Ref B: CAI30EDGE0522 Ref C: 2025-06-04T17:32:57Z"}, "edge_wallet": {"passwords": {"password_lost_report_date": "13393518443528899"}, "trigger_funnel": {"records": []}}, "enterprise_profile_guid": "7c5e522c-5c1a-47bc-8df9-0fe768723789", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "137.0.3296.52", "pdf_upsell_triggered": false, "pinned_extension_migration": true, "pinned_extensions": []}, "family_safety": {"activity_reporting_enabled": false, "web_filtering_enabled": false}, "fsd": {"retention_policy_last_version": 137}, "google": {"services": {"consented_to_sync": true, "signin": {"LAST_SIGNIN_ACCESS_POINT": {"time": "2025-06-04T13:46:55.834Z", "value": "17"}}}}, "import_items_failure_state": {"reimport": {"ie_react": 62436}}, "intl": {"selected_languages": "en-US,en"}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "ZxkNRY/xDObviu9/DpKPrKi/xjhMDzsAyvWG1h3i+wYX87WY1mWXXdeSKZwRkcwxcj1TLDIKQ3lO2adVHrvauw=="}, "muid": {"last_sync": "13393518413810678", "values_seen": []}, "ntp": {"next_site_suggestions_available": false, "num_personal_suggestions": 1}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_INSIGHTS": true, "PRICE_TRACKING": true, "SALIENT_IMAGE": true, "SHOPPING_DISCOUNTS": true, "SHOPPING_PAGE_TYPES": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "personalization_data_consent": {"personalization_in_context_consent_can_prompt": false, "personalization_in_context_count": 0}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 20, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "clear_browsing_data_cookies_exceptions": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "13393531976910196", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "edge_ad_targeting": {}, "edge_ad_targeting_data": {}, "edge_sdsm": {}, "edge_split_screen": {}, "edge_u2f_api_request": {}, "edge_user_agent_token": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {"http://localhost:60217,*": {"last_modified": "13393530405758217", "setting": {}}, "http://localhost:60644,*": {"last_modified": "13393531476051562", "setting": {"Geolocation": {"dismiss_count": 1}}}, "http://localhost:60834,*": {"last_modified": "13393531991560102", "setting": {"Geolocation": {"dismiss_count": 1}}}}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "secure_network": {}, "secure_network_sites": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"http://localhost:3000,*": {"last_modified": "13393526443352084", "setting": {"lastEngagementTime": 1.3393526443352072e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.1, "rawScore": 5.1}}, "http://localhost:59988,*": {"last_modified": "13393529514129448", "setting": {"lastEngagementTime": 1.3393529514129426e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}, "http://localhost:60217,*": {"last_modified": "13393530386519765", "setting": {"lastEngagementTime": 1.3393530386519726e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:60308,*": {"last_modified": "13393530634799526", "setting": {"lastEngagementTime": 1.3393530634799496e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}, "http://localhost:60644,*": {"last_modified": "13393531484408654", "setting": {"lastEngagementTime": 1.3393531484408616e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.8, "rawScore": 4.8}}, "http://localhost:60834,*": {"last_modified": "13393532005523640", "setting": {"lastEngagementTime": 1.3393532005523542e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.2, "rawScore": 4.2}}, "http://localhost:8080,*": {"last_modified": "13393518543553432", "setting": {"lastEngagementTime": 1.3393518543553412e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 7.***************, "rawScore": 7.***************}}}, "sleeping_tabs": {}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "tech_scam_detection": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "trackers_data": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"allowed_tracker_count": 8}}}, "tracking_org_exceptions": {}, "tracking_org_relationships": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "permission_actions": {"geolocation": [{"action": 5, "prompt_disposition": 1, "time": "*****************"}, {"action": 2, "prompt_disposition": 1, "time": "*****************"}, {"action": 2, "prompt_disposition": 1, "time": "*****************"}]}, "pref_version": 1}, "created_by_version": "137.0.3296.52", "creation_time": "*****************", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "edge_password_is_using_new_login_db_path": false, "edge_password_login_db_path_flip_flop_count": 0, "edge_profile_id": "ab492940-0a29-4ae5-a482-8f52b25ef4ae", "edge_user_with_non_zero_passwords": false, "exit_type": "Normal", "has_seen_signin_fre": false, "is_relative_to_aad": false, "last_engagement_time": "13393532005523542", "last_time_obsolete_http_credentials_removed": 1749044873.527979, "last_time_password_store_metrics_reported": 1749044843.528296, "managed_user_id": "", "name": "Profile 1", "network_pbs": {}, "observed_session_time": {"feedback_rating_in_product_help_observed_session_time_key_137.0.3296.52": 86.0}, "one_time_permission_prompts_decided_count": 3, "password_hash_data_list": [], "signin_fre_seen_time": "13393518413501830", "were_old_google_logins_removed": true}, "reset_prepopulated_engines": false, "safebrowsing": {"advanced_protection_last_refresh": "13393531976471697"}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "sessions": {"event_log": [{"crashed": false, "time": "13393518413540005", "type": 0}, {"crashed": false, "time": "13393525317660977", "type": 0}, {"crashed": false, "time": "*****************", "type": 0}, {"crashed": false, "time": "*****************", "type": 0}, {"crashed": false, "time": "*****************", "type": 0}, {"crashed": false, "time": "*****************", "type": 0}, {"crashed": false, "time": "*****************", "type": 0}, {"crashed": false, "time": "*****************", "type": 0}], "session_data_status": 1}, "shopping": {"contextual_features_enabled": true, "dma_telemetry_expiration_time": "*****************", "pcb_supported": true}, "signin": {"accounts_metadata_dict": {"fake-0003BFFD2B2C9AEC": {"BookmarksExplicitBrowserSigninEnabled": false, "ExtensionsExplicitBrowserSigninEnabled": false}}, "allowed": true, "cookie_clear_on_exit_migration_notice_complete": true, "signin_with_explicit_browser_signin_on": true, "sync_paused_start_time": "*****************"}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"apps": true, "autofill": true, "bookmarks": true, "collections": true, "collections_edge_re_evaluated": true, "collections_edge_supported": true, "edge_account_type": 5, "edge_wallet": true, "edge_wallet_edge_supported": true, "encryption_bootstrap_token_per_account_migration_done": true, "extensions": true, "extensions_edge_supported": true, "history_edge_supported": true, "keep_everything_synced": false, "passwords": true, "passwords_per_account_pref_migration_done": true, "preferences": true, "tabs": true, "tabs_edge_supported": true, "typed_urls": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "toolbar_declutter": {"new_user_cleanup_triggered": true, "undo": {"last_time": "*****************"}}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "user_experience_metrics": {"personalization_data_consent_enabled_last_known_value": false}, "video_enhancement": {"mode": "Non-AI enhancement"}, "visual_search": {"dma_state": 1}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137", "link_handling_info": {"enabled_for_installed_apps": true}}}