import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../data/services/auth_service.dart';
import '../../../data/models/user_model.dart';
import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthService _authService;

  AuthBloc({required AuthService authService})
      : _authService = authService,
        super(AuthInitial()) {
    on<AuthStarted>(_onAuthStarted);
    on<SignInRequested>(_onSignInRequested);
    on<SignUpRequested>(_onSignUpRequested);
    on<GoogleSignInRequested>(_onGoogleSignInRequested);
    on<SignOutRequested>(_onSignOutRequested);
    on<PasswordResetRequested>(_onPasswordResetRequested);
    on<ProfileUpdateRequested>(_onProfileUpdateRequested);
  }

  Future<void> _onAuthStarted(AuthStarted event, Emitter<AuthState> emit) async {
    try {
      final user = _authService.currentUser;
      if (user != null) {
        // Create a basic user model from Firebase Auth user
        final userData = UserModel(
          id: user.uid,
          email: user.email ?? '',
          name: user.displayName ?? '',
          userType: 'rider', // Default, will be updated from Firestore
          createdAt: DateTime.now(),
        );
        emit(AuthSuccess(user: userData));
      } else {
        emit(AuthUnauthenticated());
      }
    } catch (e) {
      emit(const AuthFailure(message: 'Failed to check authentication status'));
    }
  }

  Future<void> _onSignInRequested(
    SignInRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    try {
      final user = await _authService.signInWithEmailAndPassword(
        email: event.email,
        password: event.password,
      );

      if (user != null) {
        emit(AuthSuccess(user: user));
      } else {
        emit(const AuthFailure(message: 'Failed to sign in'));
      }
    } catch (e) {
      emit(AuthFailure(message: e.toString()));
    }
  }

  Future<void> _onSignUpRequested(
    SignUpRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    try {
      final user = await _authService.registerWithEmailAndPassword(
        email: event.email,
        password: event.password,
        name: event.name,
        userType: event.userType,
        phoneNumber: event.phoneNumber,
      );

      if (user != null) {
        emit(AuthSuccess(user: user));
      } else {
        emit(const AuthFailure(message: 'Failed to create account'));
      }
    } catch (e) {
      emit(AuthFailure(message: e.toString()));
    }
  }

  Future<void> _onGoogleSignInRequested(
    GoogleSignInRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    try {
      final user = await _authService.signInWithGoogle(
        userType: event.userType,
      );

      if (user != null) {
        emit(AuthSuccess(user: user));
      } else {
        emit(const AuthFailure(message: 'Google sign in was cancelled'));
      }
    } catch (e) {
      emit(AuthFailure(message: e.toString()));
    }
  }

  Future<void> _onSignOutRequested(
    SignOutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    try {
      await _authService.signOut();
      emit(AuthUnauthenticated());
    } catch (e) {
      emit(const AuthFailure(message: 'Failed to sign out'));
    }
  }

  Future<void> _onPasswordResetRequested(
    PasswordResetRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    try {
      await _authService.resetPassword(event.email);
      emit(PasswordResetSent(email: event.email));
    } catch (e) {
      emit(AuthFailure(message: e.toString()));
    }
  }

  Future<void> _onProfileUpdateRequested(
    ProfileUpdateRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    try {
      await _authService.updateUserProfile(
        name: event.name,
        phoneNumber: event.phoneNumber,
        profileImageUrl: event.profileImageUrl,
      );

      // Get updated user data
      final user = _authService.currentUser;
      if (user != null) {
        final userData = UserModel(
          id: user.uid,
          email: user.email ?? '',
          name: user.displayName ?? event.name ?? '',
          userType: 'rider', // Default
          createdAt: DateTime.now(),
        );
        emit(ProfileUpdated(user: userData));
      }
    } catch (e) {
      emit(AuthFailure(message: e.toString()));
    }
  }
}
